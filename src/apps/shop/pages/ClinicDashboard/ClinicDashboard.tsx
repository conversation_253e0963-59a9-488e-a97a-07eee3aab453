import { useAuthStore } from '@/apps/shop/stores/useAuthStore';
import { Text, Title } from '@mantine/core';
import { FEATURE_FLAGS } from '@/constants';
import { LastOrdersPanel } from './components/LastOrdersPanel/LastOrdersPanel';
import { EstimatedRebatesPanel } from './components/EstimatedRebatesPanel/EstimatedRebatesPanel';
import { OverviewPanel } from './components/OverviewPanel/OverviewPanel';
import { PromoList } from './components/PromoList/PromoList';
import { PromoDetailsModal } from './components/PromoList/PromoDetailsModal/PromoDetailsModal';

export const ClinicDashboard = () => {
  const { user } = useAuthStore();

  return (
    <div className="main gap-10">
      <div>
        <Title order={3} size="h4" mb=".25rem">
          Welcome {user?.name}
        </Title>

        <Text size="md">
          View your clinics spend, rebate tracking, and order history below.
        </Text>
      </div>
      <OverviewPanel />
      <EstimatedRebatesPanel />
      {FEATURE_FLAGS.PROMO_MATCHER && (
        <>
          <PromoList />
          <PromoDetailsModal />
        </>
      )}
      <LastOrdersPanel />
    </div>
  );
};
