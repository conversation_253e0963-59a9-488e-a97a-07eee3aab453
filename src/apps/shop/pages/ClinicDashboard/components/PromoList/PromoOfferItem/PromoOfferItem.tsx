import { OfferType } from '@/types';
import { getPriceString } from '@/utils';
import { AddToCart } from '@/libs/products/components/AddToCart';

type PromoOfferItemProps = {
  offer: OfferType;
};

export const PromoOfferItem = ({ offer }: PromoOfferItemProps) => {
  const itemPrice = offer.price || offer.clinicPrice;
  if (!itemPrice) return null;

  return (
    <div className="grid h-[72px] w-full grid-cols-[auto_1fr_auto] items-center gap-6 bg-white p-4">
      <div className="h-12">
        <img
          src={
            offer.vendor.imageUrl ||
            'https://staging.services.highfive.vet/storage/vendor-images/mwi.png'
          }
          className="h-full"
          alt={`${offer.vendor.name} logo`}
        />
      </div>
      <div>
        <p className="max-w-96 text-sm font-medium text-black">{offer.name}</p>
        <div className="flex items-center gap-4 divide-x-1 divide-solid divide-black/10">
          <span>
            <span className="text-xs text-black/65">SKU: </span>
            <span className="mr-3 text-xs font-medium text-black">
              {offer.vendorSku}
            </span>
          </span>
          <span className="text-xs text-black">{offer.vendor.name}</span>
        </div>
      </div>
      <div className="flex max-w-40 items-center gap-3">
        <span className="text-sm font-medium">{getPriceString(itemPrice)}</span>
        <AddToCart.Input offer={offer} />
      </div>
    </div>
  );
};
