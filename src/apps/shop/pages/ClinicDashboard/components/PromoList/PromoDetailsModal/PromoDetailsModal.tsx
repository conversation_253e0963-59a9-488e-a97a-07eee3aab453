import { Button } from '@/libs/ui/Button/Button';
import { Modal } from '@/components';
import { MODAL_NAME } from '@/constants';
import {
  useModalStore,
  type ModalOptionProps,
} from '@/apps/shop/stores/useModalStore';
import styles from './PromoDetailsModal.module.css';
import { PromoTitle } from '../PromoTitle/PromoTitle';
import { PromoType } from '@/types/common';
import { AddToCart } from '@/libs/products/components/AddToCart';
import { PromoOfferItem } from '../PromoOfferItem/PromoOfferItem';

type PromoOfferModalOptions = ModalOptionProps & {
  promoType: PromoType['type'];
  title: string;
  offers: PromoType['offers'];
};

export const PromoDetailsModal = () => {
  const { modalOption, closeModal } = useModalStore();
  const { promoType, title, offers } = modalOption as PromoOfferModalOptions;

  if (!offers) {
    return null;
  }

  const handleSuccess = () => {
    // Close modal on successful cart addition
    closeModal();
  };

  const handleError = (error: string) => {
    console.error('Failed to add items to cart:', error);
    // Could show a toast notification here
  };

  return (
    <Modal
      name={MODAL_NAME.PROMO_MATCHER_PRODUCTS}
      size="auto"
      withCloseButton
      customClasses={{ header: styles.modalHeader, body: styles.modalBody }}
    >
      <div className="flex flex-col">
        <div className="flex-col bg-white p-6 pt-0">
          <h3 className="mb-2 text-2xl font-medium">
            You&apos;re Almost There!
          </h3>
          <p className="mb-6 text-sm text-black/65">
            Follow the steps below to claim your savings before this offer
            expires.
          </p>
          <AddToCart onSuccess={handleSuccess} onError={handleError}>
            <div className="space-y-4 rounded-lg border-2 border-black/10 bg-black/2.5 p-6">
              <PromoTitle promoType={promoType} title={title} />
              <div className="divider-h"></div>
              <div className="grid gap-2">
                {offers.map((offer) => (
                  <PromoOfferItem key={offer.id} offer={offer} />
                ))}
              </div>
              <p className="mr-2 mb-4 inline-block text-sm">
                Buy now and get <strong>9 for free!</strong>
              </p>
              <Button className={styles.seeList} variant="unstyled">
                See List
              </Button>
              <div className="divider-h"></div>
              <div className="flex items-center justify-between">
                <Subtotal />
                <div className="w-52">
                  <AddToCart.Button fullWidth />
                </div>
              </div>
            </div>
          </AddToCart>
        </div>
      </div>
    </Modal>
  );
};
