.itemContainer {
  border: 1px solid rgba(0, 0, 0, 0.04);
  border-radius: 4px;
  transition: 0.2s;

  &:hover {
    border-color: #3958d4;
    box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.08);
  }
}

.seeList {
  display: inline-block;
  font-size: 0.75rem;
  color: #3958d4;
  font-weight: 500;
}

.modalHeader {
  height: 30px;
  min-height: 10px;
  padding: 0 0.6rem;
  padding-top: 20px;
  background-color: transparent;
}

.modalBody {
  padding: 0.4rem;
  padding-top: 0;
  min-width: 640px;
}
