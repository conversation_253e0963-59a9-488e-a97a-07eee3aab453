import DiscountIcon from '@/libs/gpo/components/SavingAlert/assets/discount.svg?react';
import { PROMO_TYPE } from '../constants';

type PromoTitleProps = {
  promoType: keyof typeof PROMO_TYPE;
  title: string;
};

export const PromoTitle = ({ promoType, title }: PromoTitleProps) => {
  return (
    <div>
      <div className="flex items-center gap-2">
        <DiscountIcon className="h-6 w-6" />
        <span className="text-sm font-medium text-black">
          Promotion • {PROMO_TYPE[promoType]}
        </span>
      </div>
      <h4 className="text-lg font-semibold text-gray-900">{title}</h4>
    </div>
  );
};
