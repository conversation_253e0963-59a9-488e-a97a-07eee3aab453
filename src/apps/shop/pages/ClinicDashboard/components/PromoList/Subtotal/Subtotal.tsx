import { useAddToCartContext } from '@/libs/products/components/AddToCart/AddToCartContext';
import { getPriceString } from '@/utils';

export const Subtotal = () => {
  const { getTotalSubtotal, getItemsArray } = useAddToCartContext();

  const subtotal = getTotalSubtotal();
  const itemsCount = getItemsArray().length;

  return (
    <div>
      <p className="mb-1 text-base">Subtotal ({itemsCount} items)</p>
      <div className="flex items-center gap-2">
        <span className="mr-1 text-[32px] font-medium">
          {getPriceString(subtotal)}
        </span>
        {/* {showOriginalPrice && originalPrice > subtotal && (
          <span className="relative mt-3 align-bottom text-base font-medium text-black/65 line-through">
            {getPriceString(originalPrice)}
          </span>
        )} */}
      </div>
    </div>
  );
};
