import { getPriceString } from '@/utils';

type SubtotalProps = {
  subtotal?: number;
  itemsCount?: number;
  originalPrice?: number;
  showOriginalPrice?: boolean;
};

export const Subtotal = ({
  subtotal = 0,
  itemsCount = 0,
  originalPrice = 240.12,
  showOriginalPrice = true,
}: SubtotalProps) => {

  return (
    <div>
      <p className="mb-1 text-base">Subtotal ({itemsCount} items)</p>
      <div className="flex items-center gap-2">
        <span className="mr-1 text-[32px] font-medium">
          {getPriceString(subtotal)}
        </span>
        {showOriginalPrice && originalPrice > subtotal && (
          <span className="relative mt-3 align-bottom text-base font-medium text-black/65 line-through">
            {getPriceString(originalPrice)}
          </span>
        )}
      </div>
    </div>
  );
};
