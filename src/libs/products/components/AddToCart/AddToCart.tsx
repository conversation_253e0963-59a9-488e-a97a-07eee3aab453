import { useCallback, useState, type ReactNode } from 'react';
import { OfferType } from '@/types';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { useCartProductMapState } from '@/libs/cart/hooks/useCartProductMapState';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';
import {
  AddToCartContext,
  type AddToCartContextType,
  type CartItem,
} from './AddToCartContext';
import { AddToCartInput } from './components/AddToCartInput/AddToCartInput';
import { AddToCartButton } from './components/AddToCartButton/AddToCartButton';

export function AddToCart({ children }: { children: ReactNode }) {
  const [items, setItems] = useState<Map<string, CartItem>>(new Map());
  const { addToCart, updatingProductIds } = useCartStore();
  const cartProductMapState = useCartProductMapState();

  const isLoading = Array.from(items.keys()).some((offerId) =>
    updatingProductIds.has(offerId),
  );

  const addItem = useCallback((offer: OfferType, quantity: number) => {
    setItems((prev) => {
      const newItems = new Map(prev);
      newItems.set(offer.id, {
        offerId: offer.id,
        offer,
        quantity,
      });
      return newItems;
    });
  }, []);

  const getTotalQuantity = useCallback(() => {
    const itemsValues = Array.from(items.values());
    return itemsValues.reduce((total, item) => total + item.quantity, 0);
  }, [items]);

  const getTotalSubtotal = useCallback(() => {
    const itemsValues = Array.from(items.values());
    return itemsValues.reduce((total, item) => {
      const { salePrice } = getProductOfferComputedData(item.offer);
      if (!salePrice) return total;
      return total + salePrice * item.quantity;
    }, 0);
  }, [items]);

  const getItemsArray = () => {
    return Array.from(items.values());
  };

  const addAllToCart = useCallback(async () => {
    const itemsValues = Array.from(items.values());
    if (itemsValues.length === 0) return;

    const promises = itemsValues.map((item) => {
      const currentQuantityInCart =
        cartProductMapState[item.offerId]?.quantity ?? 0;
      const newTotalQuantity = currentQuantityInCart + item.quantity;

      return addToCart({
        productOfferId: item.offerId,
        quantity: newTotalQuantity,
        onError: () => {},
      });
    });

    await Promise.all(promises);
  }, [items, addToCart, cartProductMapState]);

  const contextValue: AddToCartContextType = {
    items,
    isLoading,
    addItem,
    getTotalQuantity,
    getTotalSubtotal,
    getItemsArray,
    addAllToCart,
  };

  return (
    <AddToCartContext.Provider value={contextValue}>
      {children}
    </AddToCartContext.Provider>
  );
}

AddToCart.Input = AddToCartInput;
AddToCart.Button = AddToCartButton;
