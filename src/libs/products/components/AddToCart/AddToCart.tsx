import { useCallback, useState, type ReactNode } from 'react';
import { OfferType } from '@/types';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { useCartProductMapState } from '@/libs/cart/hooks/useCartProductMapState';
import { 
  AddToCartContext, 
  type AddToCartContextType, 
  type CartItem 
} from './AddToCartContext';
import { AddToCartInput } from './components/AddToCartInput/AddToCartInput';
import { AddToCartButton } from './components/AddToCartButton/AddToCartButton';
import { AddToCartSubtotal } from './components/AddToCartSubtotal/AddToCartSubtotal';
import { AddToCartItemCount } from './components/AddToCartItemCount/AddToCartItemCount';

interface AddToCartProps {
  children: ReactNode;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function AddToCart({ children, onSuccess, onError }: AddToCartProps) {
  const [items, setItems] = useState<Map<string, CartItem>>(new Map());
  const { addToCart, updatingProductIds } = useCartStore();
  const cartProductMapState = useCartProductMapState();

  const isLoading = Array.from(items.keys()).some(offerId => 
    updatingProductIds.has(offerId)
  );

  const addItem = useCallback((offer: OfferType, quantity: number) => {
    setItems(prev => {
      const newItems = new Map(prev);
      newItems.set(offer.id, {
        offerId: offer.id,
        offer,
        quantity,
      });
      return newItems;
    });
  }, []);

  const updateItem = useCallback((offerId: string, quantity: number) => {
    setItems(prev => {
      const newItems = new Map(prev);
      const existingItem = newItems.get(offerId);
      if (existingItem) {
        newItems.set(offerId, {
          ...existingItem,
          quantity,
          error: undefined, // Clear error when updating
        });
      }
      return newItems;
    });
  }, []);

  const removeItem = useCallback((offerId: string) => {
    setItems(prev => {
      const newItems = new Map(prev);
      newItems.delete(offerId);
      return newItems;
    });
  }, []);

  const setItemError = useCallback((offerId: string, error: string) => {
    setItems(prev => {
      const newItems = new Map(prev);
      const existingItem = newItems.get(offerId);
      if (existingItem) {
        newItems.set(offerId, {
          ...existingItem,
          error,
        });
      }
      return newItems;
    });
  }, []);

  const clearItemError = useCallback((offerId: string) => {
    setItems(prev => {
      const newItems = new Map(prev);
      const existingItem = newItems.get(offerId);
      if (existingItem) {
        newItems.set(offerId, {
          ...existingItem,
          error: undefined,
        });
      }
      return newItems;
    });
  }, []);

  const getTotalQuantity = useCallback(() => {
    return Array.from(items.values()).reduce((total, item) => total + item.quantity, 0);
  }, [items]);

  const getTotalSubtotal = useCallback(() => {
    return Array.from(items.values()).reduce((total, item) => {
      const price = item.offer.price || item.offer.clinicPrice;
      if (!price) return total;
      return total + (parseFloat(price) * item.quantity);
    }, 0);
  }, [items]);

  const getItemsArray = useCallback(() => {
    return Array.from(items.values());
  }, [items]);

  const addAllToCart = useCallback(async () => {
    const itemsArray = Array.from(items.values());
    
    if (itemsArray.length === 0) {
      onError?.('No items to add to cart');
      return;
    }

    try {
      // Add all items to cart in parallel
      const promises = itemsArray.map(item => {
        const currentQuantityInCart = cartProductMapState[item.offerId]?.quantity ?? 0;
        const newTotalQuantity = currentQuantityInCart + item.quantity;
        
        return addToCart({
          productOfferId: item.offerId,
          quantity: newTotalQuantity,
          onError: (message: string) => {
            setItemError(item.offerId, message);
          },
        });
      });

      await Promise.all(promises);
      
      // Check if any items have errors
      const hasErrors = Array.from(items.values()).some(item => item.error);
      
      if (!hasErrors) {
        onSuccess?.();
        reset(); // Clear items after successful addition
      }
    } catch (error) {
      onError?.('Failed to add items to cart');
    }
  }, [items, addToCart, cartProductMapState, onSuccess, onError, setItemError]);

  const reset = useCallback(() => {
    setItems(new Map());
  }, []);

  const contextValue: AddToCartContextType = {
    items,
    isLoading,
    addItem,
    updateItem,
    removeItem,
    setItemError,
    clearItemError,
    getTotalQuantity,
    getTotalSubtotal,
    getItemsArray,
    addAllToCart,
    reset,
  };

  return (
    <AddToCartContext.Provider value={contextValue}>
      {children}
    </AddToCartContext.Provider>
  );
}

// Attach sub-components to the main component
AddToCart.Input = AddToCartInput;
AddToCart.Button = AddToCartButton;
AddToCart.Subtotal = AddToCartSubtotal;
AddToCart.ItemCount = AddToCartItemCount;
