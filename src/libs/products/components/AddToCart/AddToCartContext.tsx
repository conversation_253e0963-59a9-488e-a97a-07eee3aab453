import { createContext, useContext } from 'react';
import { OfferType } from '@/types';

export interface CartItem {
  offerId: string;
  offer: OfferType;
  quantity: number;
  error?: string;
}

export interface AddToCartContextType {
  items: Map<string, CartItem>;
  isLoading: boolean;
  addItem: (offer: OfferType, quantity: number) => void;
  updateItem: (offerId: string, quantity: number) => void;
  removeItem: (offerId: string) => void;
  setItemError: (offerId: string, error: string) => void;
  clearItemError: (offerId: string) => void;
  getTotalQuantity: () => number;
  getTotalSubtotal: () => number;
  getItemsArray: () => CartItem[];
  addAllToCart: () => Promise<void>;
  reset: () => void;
}

const AddToCartContext = createContext<AddToCartContextType | null>(null);

export const useAddToCartContext = () => {
  const context = useContext(AddToCartContext);
  if (!context) {
    throw new Error('useAddToCartContext must be used within an AddToCart component');
  }
  return context;
};

export { AddToCartContext };
