import { createContext, useContext } from 'react';
import { OfferType } from '@/types';

export type CartItem = {
  offerId: string;
  offer: OfferType;
  quantity: number;
};

export type AddToCartContextType = {
  items: Map<string, CartItem>;
  isLoading: boolean;
  addItem: (offer: OfferType, quantity: number) => void;
  getTotalQuantity: () => number;
  getTotalSubtotal: () => number;
  getItemsArray: () => CartItem[];
  addAllToCart: () => Promise<void>;
};

const AddToCartContext = createContext<AddToCartContextType | null>(null);

export const useAddToCartContext = () => {
  const context = useContext(AddToCartContext);
  if (!context) {
    throw new Error(
      'useAddToCartContext must be used within an AddToCart component',
    );
  }
  return context;
};

export { AddToCartContext };
