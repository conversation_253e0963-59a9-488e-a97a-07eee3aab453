import { useEffect } from 'react';
import { OfferType } from '@/types';
import {
  AddToCartInput as BaseAddToCartInput,
  type AddToCartInputProps as BaseAddToCartInputProps,
} from '@/libs/products/components/AddToCartInput/AddToCartInput';
import { useAddToCartContext } from '../../AddToCartContext';

export type AddToCartInputProps = {
  offer: OfferType;
  hideButtons?: boolean;
  initialQuantity?: number;
};

export function AddToCartInput({
  offer,
  initialQuantity,
}: AddToCartInputProps) {
  const { items, addItem } = useAddToCartContext();

  const currentItem = items.get(offer.id);
  const currentQuantity =
    currentItem?.quantity ?? initialQuantity ?? offer.increments;

  useEffect(() => {
    if (!currentItem) {
      addItem(offer, currentQuantity);
    }
  }, [offer, currentQuantity, currentItem, addItem]);

  const handleUpdate: BaseAddToCartInputProps['onUpdate'] = ({ amount }) => {
    addItem(offer, amount);
  };

  return (
    <BaseAddToCartInput
      originalAmount={currentQuantity}
      minIncrement={offer.increments}
      onUpdate={handleUpdate}
    />
  );
}
