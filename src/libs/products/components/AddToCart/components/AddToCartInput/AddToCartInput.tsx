import { useEffect, useCallback } from 'react';
import { OfferType } from '@/types';
import { InputProps } from '@/libs/form/Input';
import { 
  AddToCartInput as BaseAddToCartInput,
  type AddToCartInputProps as BaseAddToCartInputProps
} from '@/libs/products/components/AddToCartInput/AddToCartInput';
import { useAddToCartContext } from '../../AddToCartContext';

export interface AddToCartInputProps {
  offer: OfferType;
  hideButtons?: boolean;
  size?: InputProps['size'];
  initialQuantity?: number;
}

export function AddToCartInput({
  offer,
  hideButtons = false,
  size = 'md',
  initialQuantity,
}: AddToCartInputProps) {
  const {
    items,
    addItem,
    updateItem,
    setItemError,
    clearItemError,
  } = useAddToCartContext();

  const currentItem = items.get(offer.id);
  const currentQuantity = currentItem?.quantity ?? initialQuantity ?? offer.increments;
  const currentError = currentItem?.error;

  // Initialize the item in context when component mounts
  useEffect(() => {
    if (!currentItem) {
      addItem(offer, currentQuantity);
    }
  }, [offer, currentQuantity, currentItem, addItem]);

  const handleUpdate: BaseAddToCartInputProps['onUpdate'] = useCallback(({
    amount,
    setError,
  }) => {
    // Clear any existing error first
    clearItemError(offer.id);
    
    // Update the quantity in context
    updateItem(offer.id, amount);
    
    // If there's a current error from the context, show it
    if (currentError) {
      setError(currentError);
    }
  }, [offer.id, updateItem, clearItemError, currentError]);

  // Handle external errors (from cart operations)
  useEffect(() => {
    if (currentError) {
      // The error will be displayed by the base component through its internal state
      // We just need to make sure it gets set when the context error changes
    }
  }, [currentError]);

  return (
    <BaseAddToCartInput
      originalAmount={currentQuantity}
      minIncrement={offer.increments}
      onUpdate={handleUpdate}
      hideButtons={hideButtons}
      size={size}
    />
  );
}
