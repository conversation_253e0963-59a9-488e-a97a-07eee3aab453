import { useAddToCartContext } from '../../AddToCartContext';
import { getPriceString } from '@/utils';

export interface AddToCartSubtotalProps {
  className?: string;
  showItemCount?: boolean;
  showOriginalPrice?: boolean;
  originalPrice?: number;
}

export function AddToCartSubtotal({
  className,
  showItemCount = true,
  showOriginalPrice = false,
  originalPrice,
}: AddToCartSubtotalProps) {
  const { getTotalSubtotal, getTotalQuantity, getItemsArray } = useAddToCartContext();

  const subtotal = getTotalSubtotal();
  const totalQuantity = getTotalQuantity();
  const itemsCount = getItemsArray().length;

  if (itemsCount === 0) {
    return null;
  }

  return (
    <div className={className}>
      <div className="flex items-center justify-between">
        <div>
          {showItemCount && (
            <span className="text-sm text-black/65">
              {itemsCount} item{itemsCount !== 1 ? 's' : ''} • {totalQuantity} unit{totalQuantity !== 1 ? 's' : ''}
            </span>
          )}
        </div>
        <div className="text-right">
          {showOriginalPrice && originalPrice && originalPrice > subtotal && (
            <div className="text-sm text-black/65 line-through">
              {getPriceString(originalPrice.toString())}
            </div>
          )}
          <div className="text-lg font-semibold">
            {getPriceString(subtotal.toString())}
          </div>
        </div>
      </div>
    </div>
  );
}
