import { useAddToCartContext } from '../../AddToCartContext';

export interface AddToCartItemCountProps {
  className?: string;
  showUnits?: boolean;
  format?: 'short' | 'long';
}

export function AddToCartItemCount({
  className,
  showUnits = true,
  format = 'short',
}: AddToCartItemCountProps) {
  const { getTotalQuantity, getItemsArray } = useAddToCartContext();

  const totalQuantity = getTotalQuantity();
  const itemsCount = getItemsArray().length;

  if (itemsCount === 0) {
    return null;
  }

  const renderContent = () => {
    if (format === 'long') {
      const itemText = `${itemsCount} item${itemsCount !== 1 ? 's' : ''}`;
      const unitText = showUnits ? ` • ${totalQuantity} unit${totalQuantity !== 1 ? 's' : ''}` : '';
      return `${itemText}${unitText}`;
    }
    
    // Short format
    if (showUnits) {
      return `${itemsCount}/${totalQuantity}`;
    }
    return itemsCount.toString();
  };

  return (
    <span className={className}>
      {renderContent()}
    </span>
  );
}
