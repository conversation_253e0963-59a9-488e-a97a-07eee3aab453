import { AddToCartButton as BaseAddToCartButton } from '@/libs/products/components/AddToCartForm/components/AddToCartButton/AddToCartButton';
import { useAddToCartContext } from '../../AddToCartContext';
import { useCartProductMapState } from '@/libs/cart/hooks/useCartProductMapState';

export type AddToCartButtonProps = {
  fullWidth?: boolean;
  onClick?: () => void;
};

export function AddToCartButton({
  fullWidth = false,
  onClick,
}: AddToCartButtonProps) {
  const { getItemsArray, isLoading, addAllToCart } = useAddToCartContext();
  const cartProductMapState = useCartProductMapState();

  const values = getItemsArray();
  const totalQuantityInCart = values.reduce((total, item) => {
    const quantityInCart = cartProductMapState[item.offerId]?.quantity ?? 0;
    return total + quantityInCart;
  }, 0);

  const handleClick = async () => {
    if (onClick) {
      onClick();
    } else {
      await addAllToCart();
    }
  };

  return (
    <BaseAddToCartButton
      isLoading={isLoading}
      quantityInCart={totalQuantityInCart}
      fullWidth={fullWidth}
      onClick={handleClick}
    />
  );
}
