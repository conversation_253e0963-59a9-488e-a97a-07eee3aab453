import type { Meta, StoryObj } from '@storybook/react';
import { AddToCart } from './AddToCart';
import { OfferType } from '@/types';

type Story = StoryObj<typeof AddToCart>;

// Mock offer data for stories
const mockOffer1: OfferType = {
  id: 'offer-1',
  vendor: {
    id: 'vendor-1',
    name: "Hill's Pet Nutrition",
    imageUrl: 'https://staging.services.highfive.vet/storage/vendor-images/hills.png',
    type: 'manufacturer',
  },
  name: "Hill's Science Diet Adult Dry Dog Food",
  vendorSku: 'HD-001',
  price: '45.99',
  clinicPrice: '42.99',
  stockStatus: 'IN_STOCK',
  lastOrderedAt: null,
  lastOrderedQuantity: null,
  increments: 1,
  isRecommended: true,
  rebatePercent: 5,
  product: {
    id: 'product-1',
    name: "Hill's Science Diet Adult Dry Dog Food",
    imageUrl: null,
    isFavorite: false,
    manufacturer: "Hill's Pet Nutrition",
    manufacturerSku: 'HD-001',
    description: 'Premium adult dog food',
    attributes: [],
    offers: [],
    isHazardous: false,
    requiresPrescription: false,
    requiresColdShipping: false,
    isControlledSubstance: false,
    requiresPedigree: false,
  },
};

const mockOffer2: OfferType = {
  ...mockOffer1,
  id: 'offer-2',
  name: "Hill's Science Diet Puppy Food",
  vendorSku: 'HD-002',
  price: '52.99',
  clinicPrice: '49.99',
  increments: 2,
};

const mockOffer3: OfferType = {
  ...mockOffer1,
  id: 'offer-3',
  name: "Hill's Science Diet Senior Food",
  vendorSku: 'HD-003',
  price: '48.99',
  clinicPrice: '45.99',
  increments: 1,
};

const meta: Meta<typeof AddToCart> = {
  title: 'Product/AddToCart',
  component: AddToCart,
  parameters: {
    layout: 'padded',
  },
};

export default meta;

export const BasicUsage: Story = {
  render: () => (
    <div style={{ maxWidth: '600px' }}>
      <AddToCart>
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Add Products to Cart</h3>
          
          <div className="border rounded p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">{mockOffer1.name}</p>
                <p className="text-sm text-gray-600">SKU: {mockOffer1.vendorSku}</p>
              </div>
              <div className="flex items-center gap-3">
                <span className="font-medium">${mockOffer1.clinicPrice}</span>
                <AddToCart.Input offer={mockOffer1} />
              </div>
            </div>
          </div>

          <div className="border rounded p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">{mockOffer2.name}</p>
                <p className="text-sm text-gray-600">SKU: {mockOffer2.vendorSku}</p>
              </div>
              <div className="flex items-center gap-3">
                <span className="font-medium">${mockOffer2.clinicPrice}</span>
                <AddToCart.Input offer={mockOffer2} />
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between pt-4 border-t">
            <AddToCart.Subtotal showItemCount={true} />
            <AddToCart.Button fullWidth={false} />
          </div>
        </div>
      </AddToCart>
    </div>
  ),
};

export const FlexibleComposition: Story = {
  render: () => (
    <div style={{ maxWidth: '800px' }}>
      <AddToCart>
        <div className="space-y-6">
          <h3 className="text-lg font-semibold">Flexible Product Layout</h3>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="border rounded p-4">
              <h4 className="font-medium mb-2">{mockOffer1.name}</h4>
              <AddToCart.Input offer={mockOffer1} />
            </div>
            
            <div className="border rounded p-4">
              <h4 className="font-medium mb-2">{mockOffer2.name}</h4>
              <AddToCart.Input offer={mockOffer2} />
            </div>
          </div>

          <div className="text-center">
            <p className="text-gray-600 mb-2">Some other content in between</p>
          </div>

          <div className="border rounded p-4">
            <h4 className="font-medium mb-2">{mockOffer3.name}</h4>
            <AddToCart.Input offer={mockOffer3} />
          </div>

          <div className="bg-gray-50 rounded p-4">
            <div className="flex items-center justify-between">
              <div>
                <AddToCart.ItemCount format="long" />
                <AddToCart.Subtotal showItemCount={false} />
              </div>
              <AddToCart.Button />
            </div>
          </div>
        </div>
      </AddToCart>
    </div>
  ),
};

export const PromoStyleLayout: Story = {
  render: () => (
    <div style={{ maxWidth: '600px' }}>
      <AddToCart>
        <div className="space-y-4 rounded-lg border-2 border-black/10 bg-black/2.5 p-6">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-black">Promotion • Special Offer</span>
          </div>
          <h4 className="text-lg font-semibold text-gray-900">Save on Hill's Products</h4>
          
          <div className="divider-h"></div>
          
          <div className="grid gap-2">
            {[mockOffer1, mockOffer2, mockOffer3].map((offer) => (
              <div key={offer.id} className="grid h-[72px] w-full grid-cols-[auto_1fr_auto] items-center gap-6 bg-white p-4">
                <div className="h-12">
                  <img
                    src={offer.vendor.imageUrl}
                    className="h-full"
                    alt={`${offer.vendor.name} logo`}
                  />
                </div>
                <div>
                  <p className="max-w-96 text-sm font-medium text-black">{offer.name}</p>
                  <div className="flex items-center gap-4 divide-x-1 divide-solid divide-black/10">
                    <span>
                      <span className="text-xs text-black/65">SKU: </span>
                      <span className="mr-3 text-xs font-medium text-black">{offer.vendorSku}</span>
                    </span>
                    <span className="text-xs text-black">{offer.vendor.name}</span>
                  </div>
                </div>
                <div className="flex max-w-40 items-center gap-3">
                  <span className="text-sm font-medium">${offer.clinicPrice}</span>
                  <AddToCart.Input offer={offer} />
                </div>
              </div>
            ))}
          </div>
          
          <div className="divider-h"></div>
          
          <div className="flex items-center justify-between">
            <AddToCart.Subtotal 
              showItemCount={true}
              showOriginalPrice={true}
              originalPrice={180.00}
            />
            <div className="w-52">
              <AddToCart.Button fullWidth />
            </div>
          </div>
        </div>
      </AddToCart>
    </div>
  ),
};
