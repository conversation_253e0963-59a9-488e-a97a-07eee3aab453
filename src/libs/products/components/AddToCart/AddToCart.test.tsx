import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AddToCart } from './AddToCart';
import { OfferType } from '@/types';

// Mock the cart store
jest.mock('@/apps/shop/stores/useCartStore/useCartStore', () => ({
  useCartStore: () => ({
    addToCart: jest.fn(),
    updatingProductIds: new Set(),
  }),
}));

// Mock the cart product map state
jest.mock('@/libs/cart/hooks/useCartProductMapState', () => ({
  useCartProductMapState: () => ({}),
}));

const mockOffer: OfferType = {
  id: 'test-offer-1',
  vendor: {
    id: 'vendor-1',
    name: 'Test Vendor',
    imageUrl: 'test-image.png',
    type: 'manufacturer',
  },
  name: 'Test Product',
  vendorSku: 'TEST-001',
  price: '10.00',
  clinicPrice: '9.00',
  stockStatus: 'IN_STOCK',
  lastOrderedAt: null,
  lastOrderedQuantity: null,
  increments: 1,
  isRecommended: false,
  rebatePercent: null,
  product: {
    id: 'product-1',
    name: 'Test Product',
    imageUrl: null,
    isFavorite: false,
    manufacturer: 'Test Manufacturer',
    manufacturerSku: 'TEST-001',
    description: 'Test description',
    attributes: [],
    offers: [],
    isHazardous: false,
    requiresPrescription: false,
    requiresColdShipping: false,
    isControlledSubstance: false,
    requiresPedigree: false,
  },
};

describe('AddToCart Component System', () => {
  it('renders compound components correctly', () => {
    render(
      <AddToCart>
        <AddToCart.Input offer={mockOffer} />
        <AddToCart.Button />
        <AddToCart.Subtotal />
        <AddToCart.ItemCount />
      </AddToCart>
    );

    // Check if components are rendered
    expect(screen.getByRole('spinbutton')).toBeInTheDocument(); // Input
    expect(screen.getByRole('button')).toBeInTheDocument(); // Button
  });

  it('allows flexible composition', () => {
    render(
      <AddToCart>
        <div>
          <h3>Some content</h3>
          <AddToCart.Input offer={mockOffer} />
        </div>
        <div>
          <p>More content</p>
          <AddToCart.Input offer={{ ...mockOffer, id: 'test-offer-2' }} />
        </div>
        <AddToCart.Button />
      </AddToCart>
    );

    // Should render multiple inputs
    const inputs = screen.getAllByRole('spinbutton');
    expect(inputs).toHaveLength(2);

    // Should render one button
    const buttons = screen.getAllByRole('button');
    expect(buttons.length).toBeGreaterThanOrEqual(1);
  });

  it('updates context when input values change', async () => {
    render(
      <AddToCart>
        <AddToCart.Input offer={mockOffer} />
        <AddToCart.Subtotal />
      </AddToCart>
    );

    const input = screen.getByRole('spinbutton');
    
    // Change input value
    fireEvent.change(input, { target: { value: '5' } });
    fireEvent.blur(input);

    // Wait for context update
    await waitFor(() => {
      // The subtotal should reflect the change
      // Since we can't easily test the exact subtotal calculation without mocking more,
      // we'll just verify the component doesn't crash
      expect(input).toHaveValue(5);
    });
  });

  it('handles button click correctly', () => {
    const mockOnClick = jest.fn();
    
    render(
      <AddToCart>
        <AddToCart.Input offer={mockOffer} />
        <AddToCart.Button onClick={mockOnClick} />
      </AddToCart>
    );

    const button = screen.getByRole('button');
    fireEvent.click(button);

    expect(mockOnClick).toHaveBeenCalled();
  });

  it('displays item count correctly', () => {
    render(
      <AddToCart>
        <AddToCart.Input offer={mockOffer} />
        <AddToCart.Input offer={{ ...mockOffer, id: 'test-offer-2' }} />
        <AddToCart.ItemCount format="long" />
      </AddToCart>
    );

    // Should show count for 2 items
    expect(screen.getByText(/2 item/)).toBeInTheDocument();
  });
});
