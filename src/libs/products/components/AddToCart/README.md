# AddToCart Component System

A composable, generic React component system for flexible cart management using compound component patterns.

## Overview

The AddToCart component system provides a flexible way to manage multiple product offers in a cart context. It follows a compound component pattern similar to the existing Combobox component, allowing developers to compose cart functionality in various layouts while maintaining consistent state management.

## Features

- **Compound Component Pattern**: Main wrapper with composable sub-components
- **Flexible Composition**: Place inputs and buttons anywhere within the wrapper
- **Context-Based State Management**: Shared state across all composed components
- **Batch Operations**: Single button handles multiple cart additions
- **TypeScript Support**: Full type safety and IntelliSense
- **Reusable**: Can be used across different parts of the application

## Basic Usage

```tsx
import { AddToCart } from '@/libs/products/components/AddToCart';

function ProductList({ offers }) {
  return (
    <AddToCart>
      {offers.map(offer => (
        <div key={offer.id} className="product-item">
          <h3>{offer.name}</h3>
          <span>${offer.clinicPrice}</span>
          <AddToCart.Input offer={offer} />
        </div>
      ))}
      <AddToCart.Button fullWidth />
    </AddToCart>
  );
}
```

## Components

### AddToCart (Main Wrapper)

The main component that provides context and manages state for all child components.

**Props:**
- `children: ReactNode` - Child components to render
- `onSuccess?: () => void` - Callback when items are successfully added to cart
- `onError?: (error: string) => void` - Callback when cart addition fails

### AddToCart.Input

Input component for quantity selection, wraps the existing AddToCartInput functionality.

**Props:**
- `offer: OfferType` - The product offer to add to cart
- `hideButtons?: boolean` - Hide increment/decrement buttons (default: false)
- `size?: 'sm' | 'md' | 'lg'` - Input size (default: 'md')
- `initialQuantity?: number` - Initial quantity (defaults to offer.increments)

### AddToCart.Button

Button component that handles batch cart operations for all inputs in the context.

**Props:**
- `isDisabled?: boolean` - Disable the button (default: false)
- `fullWidth?: boolean` - Make button full width (default: false)
- `onlyIcon?: boolean` - Show only icon version (default: false)
- `size?: ButtonBaseProps['size']` - Button size (default: 'md')
- `onClick?: () => void` - Custom click handler (overrides default batch add)

### AddToCart.Subtotal

Display component showing aggregated subtotal and item information.

**Props:**
- `className?: string` - Additional CSS classes
- `showItemCount?: boolean` - Show item count (default: true)
- `showOriginalPrice?: boolean` - Show original price with strikethrough (default: false)
- `originalPrice?: number` - Original price for comparison

### AddToCart.ItemCount

Display component showing item and unit counts.

**Props:**
- `className?: string` - Additional CSS classes
- `showUnits?: boolean` - Show unit count (default: true)
- `format?: 'short' | 'long'` - Display format (default: 'short')

## Advanced Usage

### Flexible Layout

```tsx
<AddToCart>
  <div className="grid grid-cols-2 gap-4">
    <AddToCart.Input offer={offer1} />
    <AddToCart.Input offer={offer2} />
  </div>
  
  <div className="my-4">
    <h3>Some other content</h3>
    <p>The inputs can be anywhere in the component tree</p>
  </div>
  
  <AddToCart.Input offer={offer3} />
  
  <div className="flex justify-between items-center">
    <AddToCart.Subtotal />
    <AddToCart.Button />
  </div>
</AddToCart>
```

### Promo Modal Style

```tsx
<AddToCart onSuccess={() => closeModal()}>
  <div className="promo-container">
    <h3>Special Offer</h3>
    
    {offers.map(offer => (
      <div key={offer.id} className="offer-row">
        <img src={offer.vendor.imageUrl} alt={offer.vendor.name} />
        <div>
          <p>{offer.name}</p>
          <span>SKU: {offer.vendorSku}</span>
        </div>
        <div>
          <span>${offer.clinicPrice}</span>
          <AddToCart.Input offer={offer} />
        </div>
      </div>
    ))}
    
    <div className="footer">
      <AddToCart.Subtotal 
        showOriginalPrice={true}
        originalPrice={originalTotal}
      />
      <AddToCart.Button fullWidth />
    </div>
  </div>
</AddToCart>
```

## Context API

The component uses React Context to manage state. You can access the context directly if needed:

```tsx
import { useAddToCartContext } from '@/libs/products/components/AddToCart';

function CustomComponent() {
  const {
    items,
    isLoading,
    getTotalQuantity,
    getTotalSubtotal,
    addAllToCart,
  } = useAddToCartContext();
  
  // Custom logic here
}
```

## Integration with Existing Components

The AddToCart system integrates seamlessly with existing cart infrastructure:

- Uses `useCartStore` for cart operations
- Leverages `useCartProductMapState` for current cart state
- Wraps existing `AddToCartInput` and `AddToCartButton` components
- Maintains compatibility with current cart APIs

## Migration from PromoOffer

To migrate from the old PromoOffer pattern:

**Before:**
```tsx
{offers.map(offer => (
  <PromoOffer key={offer.id} offer={offer} />
))}
<AddToCartButton isLoading={false} fullWidth />
```

**After:**
```tsx
<AddToCart>
  {offers.map(offer => (
    <PromoOfferItem key={offer.id} offer={offer} />
  ))}
  <AddToCart.Button fullWidth />
</AddToCart>
```

Where `PromoOfferItem` uses `<AddToCart.Input offer={offer} />` instead of the standalone `AddToCartInput`.

## Testing

The component system includes comprehensive tests covering:
- Compound component rendering
- Flexible composition patterns
- Context state management
- User interactions
- Error handling

Run tests with:
```bash
npm test AddToCart
```

## Storybook

Interactive examples are available in Storybook:
```bash
npm run storybook
```

Navigate to "Product/AddToCart" to see various composition examples.
